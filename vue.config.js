const { defineConfig } = require('@vue/cli-service');
const path = require('path');
module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    }
  },
  pages: {
    index: {
      entry: 'src/views/fullVersionApp/index.js',
      template: './public/fullVersionApp.html',
      filename: 'fullVersionApp.html',
      title: '分享app'
    }
  },
  chainWebpack: config => {
    // 为所有HTML页面添加通用样式
    config.plugin('html-index').tap(args => {
      if (!args[0].head) args[0].head = '';
      args[0].head += `
        <style>
          html, body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
        </style>
      `;
      return args;
    });
    // 为每个入口文件添加screenFitter.js
    Object.keys(module.exports.pages).forEach(pageName => {
      config
        .entry(pageName)
        .add(path.resolve(__dirname, 'src/utils/screenFitterLoader.js'))
        .end();
    });
  }
})
