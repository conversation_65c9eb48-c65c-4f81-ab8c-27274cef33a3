/**
 * 屏幕适配
 */
function screenFitter (doc, win, designWidth) {
 var html = doc.documentElement;
 function refreshRem() {
  var clientWidth = html.clientWidth;
  if (clientWidth >= designWidth) {
   html.style.fontSize = '100px';
  } else {
   html.style.fontSize = 100 * (clientWidth / designWidth) + 'px';
  }
 };
 doc.addEventListener('DOMContentLoaded', refreshRem);
};

// 从全局变量获取设计宽度，如果未设置则默认为750
const designWidth = window.DESIGN_WIDTH || 750;
screenFitter(document, window, designWidth)
console.log();

console.log(`屏幕自适应宽度初始化: ${designWidth}px`)